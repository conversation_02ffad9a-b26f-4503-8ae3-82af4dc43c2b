package terminal

import (
	"os"
	"strings"

	"github.com/mattn/go-runewidth"
	"golang.org/x/term"
)

// GetWidth returns the width of the terminal in characters.
// If the width cannot be determined, it returns a default width of 80.
func GetWidth() int {
	width, _, err := term.GetSize(int(os.Stdout.Fd()))
	if err != nil {
		// Return a default width if we can't get the actual width
		return 80
	}
	return width
}

// GetHeight returns the height of the terminal in characters.
// If the height cannot be determined, it returns a default height of 24.
func GetHeight() int {
	_, height, err := term.GetSize(int(os.Stdout.Fd()))
	if err != nil {
		// Return a default height if we can't get the actual height
		return 24
	}
	return height
}

// GetSize returns both the width and height of the terminal in characters.
// If the dimensions cannot be determined, it returns default values (80, 24).
func GetSize() (width, height int) {
	width, height, err := term.GetSize(int(os.Stdout.Fd()))
	if err != nil {
		// Return default dimensions if we can't get the actual dimensions
		return 80, 24
	}
	return width, height
}

// IsTerminal returns true if the given file descriptor is a terminal.
func IsTerminal(fd int) bool {
	return term.IsTerminal(fd)
}

// IsStdoutTerminal returns true if stdout is a terminal.
func IsStdoutTerminal() bool {
	return IsTerminal(int(os.Stdout.Fd()))
}

// IsStdinTerminal returns true if stdin is a terminal.
func IsStdinTerminal() bool {
	return IsTerminal(int(os.Stdin.Fd()))
}

// StringWidth returns the visual width of a string, taking into account
// wide characters (like CJK characters).
func StringWidth(s string) int {
	return runewidth.StringWidth(s)
}

// MaxLineWidth returns the maximum visual width of any line in the string.
func MaxLineWidth(s string) int {
	lines := strings.Split(s, "\n")
	maxWidth := 0
	for _, line := range lines {
		width := StringWidth(line)
		if width > maxWidth {
			maxWidth = width
		}
	}
	return maxWidth
}
