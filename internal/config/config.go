package config

import (
	_ "embed"
	"errors"
	"gopkg.in/yaml.v3"
	"os"
	"path/filepath"
)

const configVersion1 = "1"

var (
	FileNotFoundError = errors.New("config file not found")
)

type ModelCode string
type modelRole string

const (
	ModelMainRole       modelRole = "main"
	ModelMetaRole       modelRole = "meta"
	ModelAdditionalRole modelRole = "additional"
)

type Config struct {
	Version           string      `yaml:"version"`
	Models            []*Model    `yaml:"models"`
	DefaultParameters *Parameters `yaml:"default_parameters"`
}

type Model struct {
	Name       string       `yaml:"name,omitempty"`
	Role       modelRole    `yaml:"role,omitempty"`
	Provider   providerCode `yaml:"provider"`
	Code       ModelCode    `yaml:"model"`
	Active     bool         `yaml:"active"`
	APIBaseURL string       `yaml:"api_base_url"`
	APIKey     string       `yaml:"api_key"`
}

type Parameters struct {
	Temperature float32 `yaml:"temperature"`
	TopP        float32 `yaml:"top_p"`
	Reasoning   *Reasoning
}

type Reasoning struct {
	Effort  string `yaml:"effort"`
	Exclude string `yaml:"exclude"`
}

func (cfg *Config) GetMainModel() *Model {
	for _, model := range cfg.Models {
		if model.Role == ModelMainRole {
			return model
		}
	}
	return nil
}

func Save(cfg *Config) error {
	foundMainModel := false
	for _, model := range cfg.Models {
		if model.Role == ModelMainRole {
			foundMainModel = true
			break
		}
	}
	if !foundMainModel {
		return errors.New("main model not found")
	}

	configFile := GetMainConfigFilePath()
	configDir := filepath.Dir(configFile)
	if err := os.MkdirAll(configDir, os.ModePerm); err != nil {
		return err
	}

	cfg.Version = configVersion1
	configYAML, err := yaml.Marshal(cfg)
	if err != nil {
		return err
	}

	if err := os.WriteFile(configFile, configYAML, 0644); err != nil {
		return err
	}

	return nil
}

func Load() (*Config, error) {
	configFile := GetMainConfigFilePath()
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		return nil, FileNotFoundError
	}

	configYAML, err := os.ReadFile(configFile)
	if err != nil {
		return nil, err
	}

	var cfg *Config
	if err := yaml.Unmarshal(configYAML, &cfg); err != nil {
		return nil, err
	}

	return cfg, nil
}
