package teakeypress

import tea "github.com/charmbracelet/bubbletea"

// KeyPressModel is a simple model that quits on any keypress
type KeyPressModel struct{}

// NewKeyPressModel creates a new model that quits on any keypress
func NewKeyPressModel() KeyPressModel {
	return KeyPressModel{}
}

func (m KeyPressModel) Init() tea.Cmd {
	return nil
}

func (m KeyPressModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg.(type) {
	case tea.KeyMsg:
		return m, tea.Quit
	}
	return m, nil
}

func (m KeyPressModel) View() string {
	return ""
}

func WaitForAnyKeyPress() {
	p := tea.NewProgram(NewKeyPressModel())
	_, err := p.Run()
	if err != nil {
		panic(err)
	}
}
