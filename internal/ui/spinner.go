package ui

import (
	"github.com/charmbracelet/bubbles/spinner"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/mattn/go-runewidth"
	"github.com/oxio/aia/internal/colors"
	"strings"
	"time"
)

type SpinnerModel struct {
	Spinner     spinner.Model
	Title       string
	FrameConfig FrameConfig
}

func NewSpinnerModel(spnr spinner.Model, frameConfig FrameConfig) SpinnerModel {
	return SpinnerModel{
		Spinner:     spnr,
		FrameConfig: frameConfig,
	}
}

func (m SpinnerModel) Init() tea.Cmd {
	return m.Spinner.Tick
}

func (m SpinnerModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	spnr, cmd := m.Spinner.Update(msg)
	m.Spinner = spnr
	return m, cmd
}

func (m SpinnerModel) View() string {
	return m.Spinner.View()
}

type SpinnerWrapper struct {
	spinnerModel         SpinnerModel
	Render               func(m SpinnerModel) string
	AdjustContainerStyle func(style lipgloss.Style) lipgloss.Style
}

func (m *SpinnerWrapper) SetTitle(title string) *SpinnerWrapper {
	m.spinnerModel.Title = title
	return m
}

func NewSpinnerWrapper(spnr spinner.Model, frameConfig FrameConfig) *SpinnerWrapper {
	return &SpinnerWrapper{
		spinnerModel: NewSpinnerModel(spnr, frameConfig),
		Render: func(m SpinnerModel) string {
			return m.Spinner.View()
		},
		AdjustContainerStyle: func(style lipgloss.Style) lipgloss.Style {
			return style
		},
	}
}

func NewLoadingSpinnerWrapper(frameConfig FrameConfig) *SpinnerWrapper {
	spnr := spinner.New()
	spnr.Spinner = NightRiderSpinner()
	spnr.Style = lipgloss.NewStyle().Foreground(lipgloss.Color(colors.Lavender))

	wrapper := NewSpinnerWrapper(spnr, frameConfig).SetTitle("Loading")
	wrapper.Render = func(m SpinnerModel) string {
		spinnerStyle := lipgloss.NewStyle().Margin(m.FrameConfig.PaddingYRaw(), m.FrameConfig.PaddingXRaw())
		title := m.Title
		if title != "" {
			title = title + "\n"
			// first item in the m.Spinner.Spinner.Frames slice:
			if len(m.Spinner.Spinner.Frames) > 1 {
				titleLen := runewidth.StringWidth(m.Title)
				spinnerLen := runewidth.StringWidth(m.Spinner.Spinner.Frames[0])
				diff := titleLen - spinnerLen
				if diff > 0 {
					title = title + strings.Repeat(" ", diff/2)
				} else if diff < 0 {
					title = strings.Repeat(" ", -diff/2) + title
				}
			}
		}
		return spinnerStyle.Render(title + m.Spinner.View())
	}
	wrapper.AdjustContainerStyle = func(style lipgloss.Style) lipgloss.Style {
		return style.Align(lipgloss.Center)
	}

	return wrapper
}

func NewThinkingSpinnerWrapper(frameConfig FrameConfig) *SpinnerWrapper {
	spnr := spinner.New()
	spnr.Spinner = spinner.Points
	spnr.Style = lipgloss.NewStyle().Foreground(lipgloss.Color(colors.Lavender))

	wrapper := NewSpinnerWrapper(spnr, frameConfig).SetTitle("Thinking")
	wrapper.Render = func(m SpinnerModel) string {
		spinnerStyle := lipgloss.NewStyle().Margin(m.FrameConfig.PaddingYRaw(), m.FrameConfig.PaddingXRaw())
		title := m.Title
		if title != "" {
			title = title + " "
		}
		return spinnerStyle.Render(title + m.Spinner.View())
	}

	return wrapper
}

type SpinnerManagerModel struct {
	active *SpinnerWrapper
}

func NewSpinnerManagerModel() *SpinnerManagerModel {
	return &SpinnerManagerModel{}
}

func (m *SpinnerManagerModel) GetTick() tea.Cmd {
	if m.active == nil {
		return nil
	}
	return m.active.spinnerModel.Spinner.Tick
}

func (m *SpinnerManagerModel) AdjustContainerStyle(style lipgloss.Style) lipgloss.Style {
	if m.active == nil {
		return style
	}
	return m.active.AdjustContainerStyle(style)
}

func (m *SpinnerManagerModel) Init() tea.Cmd {
	return nil
}

func (m *SpinnerManagerModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case *SpinnerWrapper:
		if m.active != msg {
			m.active = msg
			return m, m.GetTick()
		}
	}

	switch msg := msg.(type) {
	case spinner.TickMsg:
		spnr, cmd := m.active.spinnerModel.Spinner.Update(msg)
		m.active.spinnerModel.Spinner = spnr
		return m, cmd
	}

	return m, nil
}

func (m *SpinnerManagerModel) View() string {
	if m.active == nil {
		return ""
	}
	return m.active.Render(m.active.spinnerModel)
}

func NightRiderSpinner() spinner.Spinner {
	return spinner.Spinner{
		Frames: []string{
			"▰▱▱▱▱▱",
			"▱▰▱▱▱▱",
			"▱▱▰▱▱▱",
			"▱▱▱▰▱▱",
			"▱▱▱▱▰▱",
			"▱▱▱▱▱▰",
			"▱▱▱▱▰▱",
			"▱▱▱▰▱▱",
			"▱▱▰▱▱▱",
			"▱▰▱▱▱▱",
		},
		FPS: time.Second / 7, //nolint:gomnd
	}
}
