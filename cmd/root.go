package cmd

import (
	"errors"
	"fmt"
	"github.com/oxio/aia/cmd/cliprompt"
	"github.com/oxio/aia/cmd/cmdcommon"
	"github.com/oxio/aia/cmd/modelconfigwizard"
	"github.com/oxio/aia/internal/config"
	"github.com/oxio/aia/internal/teakeypress"
	"github.com/oxio/aia/internal/ui"
	"os"
	"strings"

	"github.com/spf13/cobra"
)

func newRootCmd(appVersion string) *cobra.Command {

	var helpFlag bool
	var versionFlag bool

	var cmd = &cobra.Command{
		Use:     "aia",
		Short:   "AIA - The AI Assistant for CLI",
		Version: appVersion,
		RunE: func(cmd *cobra.Command, args []string) (err error) {
			var cfg *config.Config
			actionToRun := cliprompt.ActionName

			actions := map[string]func() error{

				modelconfigwizard.ActionName: func() error {
					err = modelconfigwizard.Run(cmd.Context(), cfg)
					if errors.Is(err, cmdcommon.UserQuitError) {
						fmt.Println("StreamUserMessage quited")
						return nil
					}
					return err
				},

				cliprompt.ActionName: func() error {
					return cliprompt.Run(strings.Join(args, " "), cfg)
				},
			}

			if modelconfigwizard.IsAction(args) {
				actionToRun = modelconfigwizard.ActionName
			}

			cfg, err = config.Load()
			if err != nil && errors.Is(err, config.FileNotFoundError) {

				msg := "Config file not found.\nLet's take a moment to configure AIA's main model."
				msg += "\n\nPress any key to continue..."
				ui.ShowInfoMessage(msg)
				teakeypress.WaitForAnyKeyPress()

				if actionToRun == modelconfigwizard.ActionName {
					cfg = &config.Config{}
					err = nil
				}
			}

			return actions[actionToRun]()
		},
	}

	cmd.Flags().BoolVarP(&helpFlag, "help", "h", false, "ShowMessage help")
	cmd.Flags().BoolVar(&versionFlag, "version", false, "ShowMessage version")
	//cmd.Flags().BoolVar(&metaFlag, "raw", false, "Execute raw prompt. This skips any agents and plugins and executes the prompt against the meta model if it is configured. If no meta model is configured then it executes against main model.")
	//cmd.Flags().StringVarP(&modelFlag, "model", "m", false, "Specify model to use")
	//cmd.Flags().StringVarP(&agentFlag, "agent", "a", false, "Specify agent to use")

	return cmd
}

func Execute(appVersion string) {
	err := newRootCmd(appVersion).Execute()
	if err != nil {
		os.Exit(1)
	}
}
