package modelconfigwizard

import (
	"github.com/charmbracelet/bubbles/list"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/oxio/aia/internal/config"
)

type chooseProviderView struct {
	providersList  list.Model
	chosenProvider *config.Provider
	root           *rootView
	next           subView
}

type providerItem struct {
	provider *config.Provider
}

func (i providerItem) Title() string       { return i.provider.Name }
func (i providerItem) Description() string { return i.provider.Description }
func (i providerItem) FilterValue() string { return i.provider.Name }

func newChooseProviderView(title string, providers *config.Providers) *chooseProviderView {
	var items []list.Item
	for provider := range providers.Values() {
		items = append(items, providerItem{provider: &provider})
	}

	listView := list.New(items, list.NewDefaultDelegate(), 0, 0)
	listView.Title = title

	return &chooseProviderView{
		providersList:  listView,
		chosenProvider: nil,
	}
}

func (v *chooseProviderView) Prev() subView {
	return nil
}

func (v *chooseProviderView) Next() subView {
	return v.next
}

func (v *chooseProviderView) Init() tea.Cmd {
	return nil
}

func (v *chooseProviderView) View() string {
	return v.providersList.View()
}

func (v *chooseProviderView) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch keypress := msg.String(); keypress {
		case "q", "esc", "ctrl+c":
			v.root.quitting = true
			return v, tea.Quit
		case "enter":
			i, ok := v.providersList.SelectedItem().(providerItem)
			if ok {
				v.chosenProvider = i.provider
			}
			if v.next != nil {
				v.root.currentView = v.next
				return v.root.Update(msg)
			}
			return v, tea.Quit
		}
	case tea.WindowSizeMsg:
		x, y := configStyle.GetFrameSize()
		v.providersList.SetSize(msg.Width-x, msg.Height-y)
	}

	var cmd tea.Cmd
	v.providersList, cmd = v.providersList.Update(msg)
	return v, cmd
}
