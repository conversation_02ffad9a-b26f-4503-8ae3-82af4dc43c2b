package modelconfigwizard

import (
	"context"
	"errors"
	"fmt"
	teaList "github.com/charmbracelet/bubbles/list"
	"github.com/charmbracelet/bubbles/spinner"
	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/oxio/aia/pkg/common"
	"github.com/oxio/aia/pkg/openaiservice"
	"strings"
	"time"
)

var (
	layoutStyles     = teaList.DefaultStyles()
	itemStyles       = teaList.NewDefaultItemStyles()
	blurredStyle     = lipgloss.NewStyle().Foreground(lipgloss.Color("240"))
	focusedStyle     = itemStyles.SelectedTitle.UnsetBorderStyle().Padding(0)
	focusedStyleLite = itemStyles.SelectedDesc.UnsetBorderStyle().Padding(0)
	errorStyle       = lipgloss.NewStyle().Foreground(lipgloss.Color("196"))
	errorStyleLite   = lipgloss.NewStyle().Foreground(lipgloss.Color("196"))
	noStyle          = lipgloss.NewStyle()
	spinnerStyle     = lipgloss.NewStyle().Foreground(lipgloss.Color("69"))
)

type credsView struct {
	title            string
	root             *rootView
	prev             subView
	next             subView
	inputs           *[]*credItem
	focusIndex       int
	apiBaseURL       string
	apiKey           string
	status           string
	statusChan       chan string
	err              error
	loadedModels     *[]string
	modelsLoadedHook func(models *[]string)
	ctx              context.Context
	program          *tea.Program
	spnr             spinner.Model
}

type credItemId string

const (
	urlId credItemId = "url"
	keyId credItemId = "key"
)

type credItem struct {
	id        credItemId
	textModel *textinput.Model
}

func newCredsView(ctx context.Context, title, baseUrl, apiKey string) *credsView {
	v := &credsView{
		title:        title,
		focusIndex:   -1,
		ctx:          ctx,
		statusChan:   make(chan string, 1),
		spnr:         spinner.New(),
		inputs:       &[]*credItem{},
		loadedModels: &[]string{},
	}

	v.spnr.Style = spinnerStyle
	v.spnr.Spinner = spinner.Points

	urlInput := textinput.New()
	urlInput.Prompt = "Base URL: "
	urlInput.Placeholder = "https://..."
	urlInput.Focus()
	urlInput.SetValue(baseUrl)
	*v.inputs = append(*v.inputs, &credItem{id: urlId, textModel: &urlInput})

	apiKeyInput := textinput.New()
	apiKeyInput.Placeholder = "53cr3t..."
	apiKeyInput.Prompt = "API Key:  "
	apiKeyInput.EchoMode = textinput.EchoPassword
	apiKeyInput.EchoCharacter = '•'
	apiKeyInput.SetValue(apiKey)
	*v.inputs = append(*v.inputs, &credItem{id: keyId, textModel: &apiKeyInput})

	return v
}

func (v *credsView) Prev() subView {
	return v.prev
}

func (v *credsView) Next() subView {
	return nil
}

func (v *credsView) Init() tea.Cmd {
	return v.spnr.Tick
}

func (v *credsView) View() string {
	var b strings.Builder

	b.WriteString(layoutStyles.Title.Render(v.title))
	b.WriteString("\n\n")

	for _, i := range *v.inputs {
		b.WriteString(i.textModel.View())
		b.WriteRune('\n')
	}

	if v.status != "" {
		_, _ = fmt.Fprintf(&b, "\n%s %s\n\n", v.status, v.spnr.View())
	} else {
		submitBtn := fmt.Sprintf("[ %s ]", blurredStyle.Render("Continue"))
		if v.focusIndex == len(*v.inputs) {
			submitBtn = fmt.Sprintf("[ %s ]", focusedStyle.Render("Continue"))
		}
		_, _ = fmt.Fprintf(&b, "\n%s\n\n", submitBtn)
	}

	if v.err != nil {
		_, _ = fmt.Fprintf(&b, "\n%s %s\n\n", errorStyleLite.Render("Error:"), errorStyle.Render(v.err.Error()))
	}

	return b.String()
}

type modelLoadFinish struct {
	tea.Msg
	success bool
}

func (v *credsView) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "esc", "ctrl+c":
			v.root.quitting = true
			return v, tea.Quit
		// Set focus to next input
		case "tab", "shift+tab", "enter", "up", "down":
			v.err = nil
			v.status = ""
			s := msg.String()

			// Did the user press enter while the submit button was focused?
			// If so, exit.
			if s == "enter" && v.focusIndex == len(*v.inputs) {
				v.HandleModelsLoad()
			}

			// Cycle indexes
			if s == "up" || s == "shift+tab" {
				v.focusIndex--
			} else if s != "0" {
				v.focusIndex++
			}

			if v.focusIndex > len(*v.inputs) {
				v.focusIndex = 0
			} else if v.focusIndex < 0 {
				v.focusIndex = len(*v.inputs)
			}

			cmds := make([]tea.Cmd, len(*v.inputs))
			var apiKeyItem, urlItem *credItem

			for _, i := range *v.inputs {
				if i.id == keyId {
					apiKeyItem = i
				} else if i.id == urlId {
					urlItem = i
				}
			}
			v.apiBaseURL = urlItem.textModel.Value()
			v.apiKey = apiKeyItem.textModel.Value()

			apiKeyWasFocused := apiKeyItem.textModel.Focused()
			var currentFocus credItemId

			for i := 0; i <= len(*v.inputs)-1; i++ {
				model := (*v.inputs)[i].textModel
				if i == v.focusIndex {
					// Set focused state
					cmds[i] = model.Focus()
					model.PromptStyle = focusedStyleLite
					model.TextStyle = focusedStyle
					continue
				}
				// Remove focused state
				model.Blur()
				model.PromptStyle = noStyle
				model.TextStyle = noStyle
			}

			if apiKeyWasFocused && currentFocus != apiKeyItem.id {
				apiKeyItem.textModel.Placeholder = ""
			}

			return v, tea.Batch(cmds...)
		}
	case spinner.TickMsg:
		var cmd tea.Cmd
		v.spnr, cmd = v.spnr.Update(msg)
		return v, cmd
	case modelLoadFinish:
		v.status = ""
		if msg.success {
			v.root.currentView = v.next
			if v.modelsLoadedHook != nil {
				v.modelsLoadedHook(v.loadedModels)
			}
			err := v.program.RestoreTerminal()
			if err != nil {
				panic(err)
			}
		}
	}

	cmds := make([]tea.Cmd, len(*v.inputs))
	for _, i := range *v.inputs {
		inp, cmd := i.textModel.Update(msg)
		i.textModel = &inp
		cmds = append(cmds, cmd)
	}
	cmds = append(cmds, v.spnr.Tick)

	return v, tea.Batch(cmds...)
}

func (v *credsView) HandleModelsLoad() {
	ctx, cancel := context.WithTimeout(v.ctx, 4*time.Second)

	v.status = "Loading models"

	errChan := make(chan error, 1)
	msgChan := make(chan []string, 1)

	go v.loadModels(ctx, errChan, msgChan)

	go func() {
		defer close(errChan)
		defer close(msgChan)
		defer cancel()

		select {
		case err := <-errChan:
			v.status = ""
			v.err = err
			v.program.Send(modelLoadFinish{success: false})
		case models := <-msgChan:
			v.status = ""
			*v.loadedModels = models
			v.program.Send(modelLoadFinish{success: true})
		case <-ctx.Done():
			v.status = ""
			if errors.Is(ctx.Err(), context.DeadlineExceeded) {
				v.err = fmt.Errorf("operation timed out")
			} else {
				v.err = ctx.Err()
			}
			v.program.Send(modelLoadFinish{success: false})
		}
	}()
}

func (v *credsView) loadModels(ctx context.Context, errChan chan error, msgChan chan []string) {
	svc := openaiservice.New(common.HttpClientConfig{
		BaseUrl: v.apiBaseURL,
		APIKey:  v.apiKey,
	})

	select {
	case <-ctx.Done():
		errChan <- ctx.Err()
		return
	default:
		availableModels, err := svc.GetAvailableModels(v.ctx)
		if err != nil {
			errChan <- err
			return
		}
		if len(availableModels) == 0 {
			errChan <- fmt.Errorf("no available models found")
			return
		}

		msgChan <- availableModels
	}
}
