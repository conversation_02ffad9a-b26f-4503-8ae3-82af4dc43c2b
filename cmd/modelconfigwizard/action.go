package modelconfigwizard

import (
	"context"
	"fmt"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/oxio/aia/cmd/cmdcommon"
	"github.com/oxio/aia/internal/ui"
	"slices"
	"strings"

	"github.com/oxio/aia/internal/config"
)

const ActionName = "model-config-wizard"

func IsAction(args []string) bool {
	if len(args) != 1 {
		return false
	}
	firstArg := strings.ToLower(strings.TrimSpace(args[0]))
	if !slices.Contains([]string{"config", "setup", "configure"}, firstArg) {
		return false
	}
	return true
}

func Run(ctx context.Context, cfg *config.Config) error {
	var mainModel *config.Model

	if len(cfg.Models) == 0 {
		mainModel = &config.Model{}
		cfg.Models = append(cfg.Models, mainModel)
	} else {
		for _, model := range cfg.Models {
			if mainModel == nil {
				// ensure that main model is always defined: by default use the first one
				mainModel = model
			}
			if model.Role == config.ModelMainRole {
				mainModel = model
				break
			}
		}
	}

	mainModel.Name = "main"
	mainModel.Role = config.ModelMainRole

	err := setupModel(ctx, mainModel)
	if err != nil {
		return err
	}

	return config.Save(cfg)
}

func setupModel(ctx context.Context, cfgModel *config.Model) error {
	// TODO: 1) initially select the provider based on current config
	// TODO: 2) initially select the model based on current config

	cpTitle := "(1/3) Setup new model"
	if cfgModel.Name != "" {
		cpTitle = "(1/3) Setup model: " + cfgModel.Name
	}
	vProvider := newChooseProviderView(cpTitle, config.PredefinedProviders)
	vCreds := newCredsView(ctx, "(2/3) Setup API access", cfgModel.APIBaseURL, cfgModel.APIKey)
	vModel := newChooseModelView("(3/3) Choose model")
	vRoot := newRootView(3, vProvider)

	vProvider.root = vRoot
	vProvider.next = vCreds

	vCreds.root = vRoot
	vCreds.prev = vProvider
	vCreds.next = vModel
	vCreds.modelsLoadedHook = vModel.SetLoadedModels

	vModel.root = vRoot
	vModel.prev = vCreds

	p := tea.NewProgram(vRoot)
	vCreds.program = p

	if _, err := p.Run(); err != nil {
		return err
	}

	if vRoot.quitting {
		return cmdcommon.UserQuitError
	}

	cfgModel.Provider = vProvider.chosenProvider.Code
	cfgModel.APIBaseURL = vCreds.apiBaseURL
	cfgModel.APIKey = vCreds.apiKey
	cfgModel.Code = config.ModelCode(vModel.chosenModel)

	//msg := fmt.Sprintf("✨ `%s` model set to `%s`", string(cfgModel.Role), string(cfgModel.Code))
	msg := fmt.Sprintf("The `%s` model was set to `%s`", string(cfgModel.Role), string(cfgModel.Code))
	ui.ShowMessage(msg, ui.SuccessStyle().WithRichText())

	return nil
}
