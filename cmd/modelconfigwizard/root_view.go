package modelconfigwizard

import (
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

var configStyle = lipgloss.NewStyle().Margin(1, 2)

func newRootView(steps int, currentView subView) *rootView {
	return &rootView{
		steps:       steps,
		currentView: currentView,
	}
}

type rootView struct {
	steps       int
	currentView subView
	quitting    bool
}

type subView interface {
	tea.Model
	Prev() subView
	Next() subView
}

func (r *rootView) Init() tea.Cmd {
	return tea.SetWindowTitle("AIA - Configuration")
}

func (r *rootView) View() string {
	return configStyle.Render(r.currentView.View())
}

func (r *rootView) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	// do not handle "esc" press event on the root level, as it interferes
	// with specific functions of the subviews (like exiting filtering mode in lists)
	_, cmd := r.currentView.Update(msg)
	return r, cmd
}
