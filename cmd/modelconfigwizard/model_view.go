package modelconfigwizard

import (
	"github.com/charmbracelet/bubbles/list"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

type chooseModelView struct {
	modelsList   list.Model
	chosenModel  string
	loadedModels *[]string
	root         *rootView
	prev         subView
}

type modelItem struct {
	modelName string
}

func (i modelItem) Title() string       { return i.modelName }
func (i modelItem) Description() string { return "" }
func (i modelItem) FilterValue() string { return i.modelName }

func newChooseModelView(title string) *chooseModelView {
	delegate := list.NewDefaultDelegate()
	delegate.SetSpacing(0)
	delegate.Styles.SelectedTitle = delegate.Styles.SelectedTitle.
		UnsetBorderStyle().
		Padding(0, 0, 0, 1).
		BorderStyle(lipgloss.Border{Left: ">"})
	delegate.Styles.NormalTitle = delegate.Styles.NormalTitle.UnsetBorderStyle().Padding(0, 0, 0, 2)
	delegate.ShowDescription = false

	listView := list.New([]list.Item{}, delegate, 0, 0)
	listView.Title = title

	return &chooseModelView{
		modelsList:  listView,
		chosenModel: "",
	}
}

func (v *chooseModelView) SetLoadedModels(models *[]string) {
	var items []list.Item
	for _, modelName := range *models {
		items = append(items, modelItem{modelName})
	}

	v.modelsList.SetItems(items)
	v.loadedModels = models
}

func (v *chooseModelView) Prev() subView {
	return v.prev
}

func (v *chooseModelView) Next() subView {
	return nil
}

func (v *chooseModelView) Init() tea.Cmd {
	return nil
}

func (v *chooseModelView) View() string {
	return v.modelsList.View()
}

func (v *chooseModelView) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch keypress := msg.String(); keypress {
		case "q", "esc", "ctrl+c":
			v.root.quitting = true
			return v, tea.Quit
		case "enter":
			i, ok := v.modelsList.SelectedItem().(modelItem)
			if ok {
				v.chosenModel = i.modelName
			}
			return v, tea.Quit
		}
	case tea.WindowSizeMsg:
		x, y := configStyle.GetFrameSize()
		v.modelsList.SetSize(msg.Width-x, msg.Height-y)
	}

	var cmd tea.Cmd
	v.modelsList, cmd = v.modelsList.Update(msg)
	return v, cmd
}
