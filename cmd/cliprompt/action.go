package cliprompt

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/oxio/aia/internal/config"
	"github.com/oxio/aia/internal/ui"
	log "github.com/sirupsen/logrus"
	"io"
	"net/http"
	"strings"
	"time"
)

const ActionName = "cli-prompt"

func Run(prompt string, cfg *config.Config) (err error) {
	prompt = strings.TrimSpace(prompt)
	if prompt == "" {
		ui.ShowInfoMessage("Please provide a prompt.")
		return nil
	}

	// Check if config is nil (might happen if config file not found)
	if cfg == nil {
		ui.ShowWarningMessage("Configuration not found.\nPlease configure AIA first by running `aia --config`")
		return nil
	}

	var modelCfg *config.Model

	for _, model := range cfg.Models {
		if model.Role == config.ModelMainRole {
			modelCfg = model
			break
		}
	}

	if modelCfg == nil {
		ui.ShowWarningMessage("Cannot find active main model.\nPlease configure it first by running `aia --config`")
		return nil
	}

	ui.ShowUserMessage(prompt)
	return makeStreamingRequest(prompt, modelCfg)
}

// Message represents a message in the chat completion request
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatCompletionRequest represents the request body for a chat completion
type ChatCompletionRequest struct {
	Model     string    `json:"model"`
	Messages  []Message `json:"messages"`
	Stream    bool      `json:"stream"`
	MaxTokens int       `json:"max_tokens,omitempty"`
}

// StreamResponse represents a chunk of the streaming response
type StreamResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Delta struct {
			Content string `json:"content"`
		} `json:"delta"`
		Index        int    `json:"index"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
}

func makeStreamingRequest(prompt string, modelCfg *config.Model) error {
	log.Debug("Making streaming request to ", modelCfg.APIBaseURL)

	// Create the request body
	reqBody := ChatCompletionRequest{
		Model: string(modelCfg.Code),
		Messages: []Message{
			{Role: "user", Content: prompt},
		},
		Stream: true,
	}

	// Convert the request body to JSON
	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		log.Error("ShowErrorMessage marshaling request body: ", err)
		ui.ShowErrorMessage(err.Error())
		return err
	}

	// Create the HTTP request
	apiURL := strings.TrimSuffix(modelCfg.APIBaseURL, "/") + "/chat/completions"
	log.Debug("Making request to: ", apiURL)
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		log.Error("ShowErrorMessage creating request: ", err)
		ui.ShowErrorMessage(err.Error())
		return err
	}

	// Set the headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+modelCfg.APIKey)

	// Create an HTTP client with timeout
	client := &http.Client{
		Timeout: 60 * time.Second,
	}

	// Send the request
	resp, err := client.Do(req)
	if err != nil {
		log.Error("ShowErrorMessage sending request: ", err)
		ui.ShowErrorMessage(err.Error())
		return err
	}
	defer resp.Body.Close()

	// Check the response status code
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Error("ShowErrorMessage response: ", resp.Status, string(body))
		ui.ShowErrorMessage(fmt.Sprintf("ShowErrorMessage: %s\n%s", resp.Status, string(body)))
		return fmt.Errorf("error response: %s", resp.Status)
	}

	ui.StreamAia(resp.Body)

	return nil
}
