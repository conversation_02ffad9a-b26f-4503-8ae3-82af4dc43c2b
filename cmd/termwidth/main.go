package main

import (
	"fmt"
	"strings"

	"github.com/oxio/aia/internal/terminal"
)

func main() {
	// Get the terminal width
	width := terminal.GetWidth()

	// Create a horizontal line that spans the terminal width
	line := strings.Repeat("=", width)

	// Print a header with the line
	fmt.Println(line)
	fmt.Printf("%s Terminal Width Demo %s\n", strings.Repeat(" ", (width-20)/2), strings.Repeat(" ", (width-20)/2))
	fmt.Println(line)

	// Print some information
	fmt.Printf("Terminal width: %d characters\n", width)
	fmt.Printf("Terminal height: %d characters\n", terminal.GetHeight())

	// Create different width bars to demonstrate the terminal width
	fmt.Println("\nWidth visualization:")

	// 25% width bar
	quarterWidth := int(float64(width) * 0.25)
	fmt.Printf("25%% width: [%s]\n", strings.Repeat("#", quarterWidth))

	// 50% width bar
	halfWidth := int(float64(width) * 0.5)
	fmt.Printf("50%% width: [%s]\n", strings.Repeat("#", halfWidth))

	// 75% width bar
	threeQuarterWidth := int(float64(width) * 0.75)
	fmt.Printf("75%% width: [%s]\n", strings.Repeat("#", threeQuarterWidth))

	// 100% width bar
	fmt.Printf("100%% width: [%s]\n", strings.Repeat("#", width-12)) // Subtract 12 for the "100% width: []" text

	// Print a footer
	fmt.Println(line)
}
