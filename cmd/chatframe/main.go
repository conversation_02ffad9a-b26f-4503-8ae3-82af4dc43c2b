package main

import (
	"fmt"
	"github.com/oxio/aia/internal/teaframe"
)

func main() {
	// Demonstrate a chat conversation with alternating frames

	// First exchange
	teaframe.UserChatMessage("Hello! This is a message from the left side.\nIt has a blue border and is aligned to the left.")
	teaframe.AiaChatMessage("Hi there! This is a response from the right side.\nIt has a vibrant pink border and is aligned to the right.")

	// Second exchange
	teaframe.UserChatMessage("How are you doing today?")
	teaframe.AiaChatMessage("I'm doing great, thanks for asking!")

	// Third exchange with longer messages to demonstrate wrapping
	teaframe.UserChatMessage("I'm working on a project that requires creating a chat-like interface in the terminal. The messages should be displayed in frames with different colors and alignments to distinguish between participants.")
	teaframe.AiaChatMessage("That sounds interesting! Using different colors and alignments is a great way to visually distinguish between participants in a conversation. It makes the chat much more readable and intuitive.")

	// Print a message after the frames to show they've completed
	fmt.Println("Chat conversation demonstration complete.")
}
